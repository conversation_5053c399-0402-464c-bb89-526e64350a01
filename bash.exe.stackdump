Stack trace:
Frame         Function      Args
0007FFFFA990  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFA990, 0007FFFF9890) msys-2.0.dll+0x2118E
0007FFFFA990  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFFA990  0002100469F2 (00021028DF99, 0007FFFFA848, 0007FFFFA990, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA990  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA990  00021006A545 (0007FFFFA9A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFA9A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB70C20000 ntdll.dll
7FFB6F400000 KERNEL32.DLL
7FFB6E120000 KERNELBASE.dll
7FFB6A760000 apphelp.dll
7FFB707C0000 USER32.dll
7FFB6E030000 win32u.dll
7FFB70990000 GDI32.dll
7FFB6E510000 gdi32full.dll
7FFB6DD70000 msvcp_win.dll
7FFB6E650000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB6E9C0000 advapi32.dll
7FFB6F5F0000 msvcrt.dll
7FFB70A70000 sechost.dll
7FFB6F4D0000 RPCRT4.dll
7FFB6D490000 CRYPTBASE.DLL
7FFB6E7A0000 bcryptPrimitives.dll
7FFB70BA0000 IMM32.DLL
